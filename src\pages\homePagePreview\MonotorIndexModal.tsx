import React, { useEffect, useState } from 'react';
import { YTHLocalization } from 'yth-ui';
import { Select, message, Input, Button, Form, Modal, Table, Tooltip } from 'antd';
import { CurrentUser } from '@/Constant';
import { IYTHColumnProps } from 'yth-ui/es/components/list';
import { queryMonitorIndexDetail } from '@/service/envApi';
import locales from '@/locales';
import formApi from '@/service/formApi';
import style from './home.module.less';

type objType = Record<string, string>;

type propsTypes = {
  open: boolean;
  onClose: () => void;
  isPark: boolean;
  selectCompany: objType;
  companyList: objType[];
};

/**
 * @description 监测指标 查看modal
 * @returns
 */
const MonotorIndexModal: React.FC<propsTypes> = (props) => {
  const [form] = Form.useForm();
  const { open, onClose, isPark, selectCompany, companyList } = props;
  const [tableList, setTableListt] = useState<Record<string, string | number>[]>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [frequencyList, setFrequencyList] = useState<objType[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  // 获取采集频率
  const getDictData: () => Promise<void> = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: 'A23A04',
      },
      currentPage: 0,
      pageSize: 0,
    });
    if (list instanceof Array) {
      setFrequencyList(list);
      form.setFieldsValue({
        frequency: list?.[0]?.remark || '',
      });
    } else {
      setFrequencyList([]);
    }
  };

  // 获取tableList数据
  const queryTableList: (value?: {
    name?: string;
    companyId?: string;
    frequency?: string;
  }) => Promise<void> = async (value) => {
    setLoading(true);
    const data: {
      code: number;
      data: Record<string, string | number>[];
      msg: string;
    } = await queryMonitorIndexDetail({
      name: value?.name ?? null,
      companyId: value?.companyId ?? null,
      frequency: value?.frequency ?? null,
    });
    if (data.code === 200 && Array.isArray(data?.data)) {
      setTableListt(data?.data || []);
      setLoading(false);
    } else {
      message.error('请求数据出错，请刷新重试或联系管理员');
      setLoading(false);
    }
  };
  useEffect(() => {
    if (open) {
      const initializeData: () => Promise<void> = async () => {
        // 先获取字典数据
        await getDictData();

        // 字典数据获取完成后再查询表格数据
        if (isPark) {
          await queryTableList({
            companyId: selectCompany?.value === 'all' ? undefined : selectCompany?.value,

          });
          form.setFieldsValue({
            companyId: selectCompany?.value === 'all' ? undefined : selectCompany?.value,
          });
        } else {
          await queryTableList({ companyId: CurrentUser()?.unitCode });
          form.setFieldsValue({ companyId: CurrentUser()?.unitCode });
        }
      };

      initializeData();
    }
    setIsModalOpen(open);
    return () => {
      form.resetFields();
    };
  }, [open, isPark, CurrentUser, selectCompany]);

  const columnsData: IYTHColumnProps[] = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 80,
      align: 'center',
      fixed: 'left',
      render: (_: any, __: any, index: number) => {
        return (currentPage - 1) * pageSize + index + 1;
      },
    },
    {
      title: '单位名称',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 200,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '设备名称',
      dataIndex: 'devideName',
      key: 'devideName',
      width: 180,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '监测指标名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '监测指标编码',
      dataIndex: 'code',
      key: 'code',
      width: 180,
      align: 'center',
      render: (v) => {
        return (
          <Tooltip title={v ?? '-'}>
            <div className={style['table-columns-title']}>{v ?? '-'}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '计量单位',
      dataIndex: 'measureUnit',
      key: 'measureUnit',
      width: 100,
      align: 'center',
    },
    {
      title: '一级阈值上限',
      dataIndex: 'firstLevelMax',
      key: 'firstLevelMax',
      width: 100,
      align: 'center',
    },
    {
      title: '一级阈值下限',
      dataIndex: 'firstLevelMin',
      key: 'firstLevelMin',
      width: 100,
      align: 'center',
    },
    {
      title: '二级阈值上限',
      dataIndex: 'secondLevelMax',
      key: 'secondLevelMax',
      width: 100,
      align: 'center',
    },
    {
      title: '二级阈值下限',
      dataIndex: 'secondLevelMin',
      key: 'secondLevelMin',
      width: 100,
      align: 'center',
    },
  ];

  const onFinish: (values: { name?: string; companyId?: string; frequency?: string }) => void = (
    values,
  ) => {
    queryTableList(values);
  };

  return (
    <Modal
      title="监测指标详情"
      visible={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        if (onClose) onClose();
      }}
      destroyOnClose
      width="70%"
      footer={null}
    >
      <Form
        name="basic"
        layout="inline"
        form={form}
        style={{ marginBottom: 15, fontSize: 14 }}
        onFinish={onFinish}
        onReset={() => {
          if (isPark) {
            queryTableList();
          } else {
            queryTableList({ companyId: CurrentUser()?.unitCode });
            form.setFieldsValue({ companyId: CurrentUser()?.unitCode });
          }
        }}
        autoComplete="off"
      >
        <Form.Item label="单位名称" name="companyId">
          <Select
            showSearch
            placeholder="请选择"
            optionFilterProp="label"
            style={{ width: 222 }}
            disabled={!isPark}
            options={companyList?.map((item) => {
              return {
                value: item.unitCode,
                label: item.unitName,
              };
            })}
          />
        </Form.Item>
        <Form.Item label="设备名称" name="name">
          <Input style={{ width: 122 }} placeholder="请输入设备名称" />
        </Form.Item>
        <Form.Item label="采集频率" name="frequency">
          <Select
            showSearch
            placeholder="请选择"
            style={{ width: 122 }}
            optionFilterProp="label"
            options={frequencyList?.map((item) => {
              return {
                value: item.remark || item.code,
                label: item.text,
              };
            })}
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="reset">
            重置
          </Button>
        </Form.Item>
      </Form>
      <Table
        loading={loading}
        dataSource={tableList}
        rowKey="id"
        className={style['device-table']}
        bordered
        rowClassName={(_, index) => {
          if (index % 2 === 0) {
            return style['even-row'];
          }
          return style['odd-row'];
        }}
        pagination={{
          current: currentPage,
          pageSize,
          showTotal: (total) => {
            return `共${total}条记录`;
          },
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size || 10);
          },
          onShowSizeChange: (current, size) => {
            setCurrentPage(current);
            setPageSize(size);
          },
        }}
        columns={columnsData}
        scroll={{ y: 500 }}
      />
    </Modal>
  );
};

export default YTHLocalization.withLocal(MonotorIndexModal, locales, YTHLocalization.getLanguage());
